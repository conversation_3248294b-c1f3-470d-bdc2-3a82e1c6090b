<?php

namespace App\Controllers;

use App\Models\APPX_ApplicationInformationModel;
use App\Models\ExerciseModel;
use App\Models\PositionsModel;
use App\Models\PositionsGroupModel;

class ApplicationReviewController extends BaseController
{
    protected $applicationModel;
    protected $exerciseModel;
    protected $positionModel;
    protected $positionGroupModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = \Config\Services::session();
        $this->applicationModel = new APPX_ApplicationInformationModel();
        $this->exerciseModel = new ExerciseModel();
        $this->positionModel = new PositionsModel();
        $this->positionGroupModel = new PositionsGroupModel();
    }

    /**
     * [POST] Batch pre-screen multiple applications.
     * Handles standard form submission, not AJAX.
     * URI: /application_review/batch_update
     *
     * Expects POST data:
     * - ids[]: Array of application IDs to update
     * - status: The status to apply (e.g., 'passed', 'failed')
     * - remarks: Optional remarks for the batch action
     */
    public function batchUpdate()
    {
        $ids = $this->request->getPost('ids');
        $status = $this->request->getPost('status');
        $remarks = trim($this->request->getPost('remarks') ?? '');

        // --- Validation ---
        if (empty($ids) || !is_array($ids)) {
            $this->session->setFlashdata('error', 'No applications selected for batch update.');
            return redirect()->back();
        }

        $allowed_statuses = ['passed', 'failed', 'pending'];
        if (empty($status) || !in_array($status, $allowed_statuses)) {
            $this->session->setFlashdata('error', 'Invalid or missing status for batch update.');
            return redirect()->back()->withInput();
        }

        // Require remarks if status is 'failed'
        if ($status === 'failed' && empty($remarks)) {
             $this->session->setFlashdata('error', 'Remarks are required when batch failing applications.');
            return redirect()->back()->withInput();
        }

        // --- Prepare Data ---
        $data = [
            'pre_screened' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $status,
            'pre_screened_remarks' => "[Batch Update] " . $remarks,
            'pre_screened_criteria_results' => null,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id')
        ];

        // --- Perform Update ---
        $successCount = 0;
        $failCount = 0;

        foreach ($ids as $id) {
            $id = (int) $id;
            if ($id > 0) {
                try {
                    if ($this->applicationModel->update($id, $data)) {
                        $successCount++;
                    } else {
                        log_message('warning', 'Batch pre-screen failed for ID ' . $id . '. Errors: ' . json_encode($this->applicationModel->errors()));
                        $failCount++;
                    }
                } catch (\Exception $e) {
                    log_message('error', 'Exception during batch pre-screening for ID ' . $id . ': ' . $e->getMessage());
                    $failCount++;
                }
            } else {
                $failCount++;
            }
        }

        // --- Set Flash Message ---
        $message = '';
        if ($successCount > 0) {
            $message .= "$successCount application(s) pre-screened successfully. ";
        }
        if ($failCount > 0) {
            $message .= "$failCount application(s) failed to update.";
        }

        if ($successCount > 0 && $failCount == 0) {
            $this->session->setFlashdata('success', $message);
        } elseif ($failCount > 0) {
            $this->session->setFlashdata('warning', $message);
        } else {
             $this->session->setFlashdata('info', 'No applications were updated.');
        }

        return redirect()->to(base_url('application_screening'));
    }

    /**
     * [GET] Display exercises in selection status for pre-screening context.
     * URI: /application_review/exercises
     *
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function exercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        $exercises = $this->exerciseModel->where('org_id', $orgId)
                                         ->where('status', 'selection')
                                         ->orderBy('exercise_name', 'ASC')
                                         ->findAll();

        $data = [
            'title' => 'Pre-Screening Exercises',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/application_pre_screening_exercise_list', $data);
    }

    /**
     * [GET] Display position groups for a specific exercise.
     * URI: /application_review/view_position_groups/{exerciseId}
     *
     * @param int $exerciseId The ID of the exercise
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function viewPositionGroups($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('application_review/exercises'));
        }

        // Get position groups associated with this exercise
        $positionGroups = $this->positionGroupModel
            ->select('
                positions_groups.*,
                COUNT(DISTINCT positions.id) as positions_count,
                COUNT(appx_application_information.id) as applications_count,
                SUM(CASE WHEN appx_application_information.pre_screened IS NULL OR appx_application_information.pre_screened = \'\' THEN 1 ELSE 0 END) as pending_count
            ')
            ->join('positions', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('appx_application_information', 'appx_application_information.position_id = positions.id AND appx_application_information.recieved_acknowledged IS NOT NULL', 'left')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->groupBy('positions_groups.id')
            ->orderBy('positions_groups.group_name', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Position Groups for Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'positionGroups' => $positionGroups
        ];

        return view('application_pre_screening/application_pre_screening_position_groups', $data);
    }

    /**
     * [GET] Display positions for a specific position group.
     * URI: /application_review/view_positions/{groupId}
     *
     * @param int $groupId The ID of the position group
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function viewPositions($groupId)
    {
        $positionGroup = $this->positionGroupModel
            ->select('positions_groups.*, exercises.exercise_name, exercises.id as exercise_id')
            ->join('exercises', 'exercises.id = positions_groups.exercise_id')
            ->find($groupId);

        if (!$positionGroup) {
            $this->session->setFlashdata('error', 'Position group not found.');
            return redirect()->to(base_url('application_review/exercises'));
        }

        // Get positions associated with this group
        $positions = $this->positionModel
            ->select('
                positions.*,
                COUNT(appx_application_information.id) as applications_count,
                SUM(CASE WHEN appx_application_information.pre_screened IS NULL OR appx_application_information.pre_screened = \'\' THEN 1 ELSE 0 END) as pending_count
            ')
            ->join('appx_application_information', 'appx_application_information.position_id = positions.id AND appx_application_information.recieved_acknowledged IS NOT NULL', 'left')
            ->where('positions.position_group_id', $groupId)
            ->groupBy('positions.id')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Positions in Group: ' . esc($positionGroup['group_name']),
            'menu' => 'applications',
            'positionGroup' => $positionGroup,
            'exercise' => [
                'id' => $positionGroup['exercise_id'],
                'exercise_name' => $positionGroup['exercise_name']
            ],
            'positions' => $positions
        ];

        return view('application_pre_screening/application_pre_screening_positions', $data);
    }

    /**
     * [GET] Display positions and application counts for a specific exercise relevant to pre-screening.
     * URI: /application_review/exercise_applications/{exerciseId}
     *
     * @param int $exerciseId The ID of the exercise
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function exerciseApplications($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);
        if (!$exercise) {
            $this->session->setFlashdata('error', 'Exercise not found.');
            return redirect()->to(base_url('application_review/exercises'));
        }

        // Get positions associated with this exercise that have applications needing pre-screening
        $positionsWithApplications = $this->applicationModel
            ->select('
                appx_application_information.position_id,
                COUNT(appx_application_information.id) as total_application_count,
                SUM(CASE WHEN appx_application_information.pre_screened IS NULL OR appx_application_information.pre_screened = \'\' THEN 1 ELSE 0 END) as pending_prescreen_count,
                positions.designation as position_name,
                positions.position_group_id,
                positions_groups.group_name as position_group_name
            ')
            ->join('positions', 'positions.id = appx_application_information.position_id')
            ->join('positions_groups', 'positions_groups.id = positions.position_group_id')
            ->where('positions_groups.exercise_id', $exerciseId)
            ->where('appx_application_information.recieved_acknowledged IS NOT NULL')
            ->groupBy('appx_application_information.position_id, positions.designation, positions.position_group_id, positions_groups.group_name')
            ->orderBy('positions.designation', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Exercise Positions for Pre-Screening: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'positionsData' => $positionsWithApplications,
        ];

        return view('application_pre_screening/application_pre_screening_exercise_positions', $data);
    }

    /**
     * [GET] Display applications for a specific position that need pre-screening.
     * URI: /application_review/position_applications/{positionId}
     *
     * @param int $positionId The ID of the position
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function positionApplications($positionId)
    {
        $position = $this->positionModel
                        ->select('positions.*, positions_groups.group_name, positions_groups.id as position_group_id, exercises.exercise_name, exercises.id as exercise_id')
                        ->join('positions_groups', 'positions_groups.id = positions.position_group_id', 'left')
                        ->join('exercises', 'exercises.id = positions_groups.exercise_id', 'left')
                        ->find($positionId);

        if (!$position) {
            $this->session->setFlashdata('error', 'Position not found.');
            $exerciseId = $this->request->getGet('exercise_id');
            if ($exerciseId) {
                 return redirect()->to(base_url('application_review/exercise_applications/' . $exerciseId));
            }
            return redirect()->to(base_url('application_review/exercises'));
        }

        // Get position group data
        $positionGroup = null;
        if (!empty($position['position_group_id'])) {
            $positionGroup = [
                'id' => $position['position_group_id'],
                'group_name' => $position['group_name']
            ];
        }

        // Get the complete exercise data
        $exercise = null;
        if (!empty($position['exercise_id'])) {
            $exerciseData = $this->exerciseModel->find($position['exercise_id']);
            if ($exerciseData) {
                $exercise = $exerciseData;
            } else {
                $exercise = [
                    'id' => $position['exercise_id'],
                    'exercise_name' => $position['exercise_name'],
                    'advertisement_no' => 'N/A'
                ];
            }
        }

        // Get all acknowledged applications for this position
        $applications = $this->applicationModel
            ->select('appx_application_information.*, applicants.fname, applicants.lname, applicants.email')
            ->join('applicants', 'applicants.applicant_id = appx_application_information.applicant_id', 'left')
            ->where('appx_application_information.position_id', $positionId)
            ->where('appx_application_information.recieved_acknowledged IS NOT NULL')
            ->orderBy('appx_application_information.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'Applications for Position: ' . esc($position['designation']),
            'menu' => 'applications',
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'applications' => $applications
        ];

        return view('application_pre_screening/application_pre_screening_position_applications', $data);
    }

    /**
     * [GET] Display all applications that have been acknowledged (regardless of pre-screening status).
     * URI: /application_review/all_acknowledged
     */
    public function allAcknowledgedApplications()
    {
        $applications = $this->applicationModel
            ->select('appx_application_information.*, applicants.fname, applicants.lname, positions.designation as position_name, users.username as prescreened_by_user')
            ->join('applicants', 'applicants.applicant_id = appx_application_information.applicant_id', 'left')
            ->join('positions', 'positions.id = appx_application_information.position_id', 'left')
            ->join('users', 'users.id = appx_application_information.pre_screened_by', 'left')
            ->where('appx_application_information.recieved_acknowledged IS NOT NULL')
            ->orderBy('appx_application_information.created_at', 'DESC')
            ->findAll();

        $data = [
            'title' => 'All Acknowledged Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('application_pre_screening/all_acknowledged_applications_list', $data);
    }

    /**
     * [GET] Display all exercises that have acknowledged applications.
     * If an ID is provided, show only the specific exercise and its applications.
     * URI: /application_review/exercise_list/{id}
     *
     * @param int|null $id Optional exercise ID to filter results
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function exerciseList($id = null)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        if ($id !== null) {
            // Get the specific exercise
            $exercise = $this->exerciseModel->find($id);
            if (!$exercise) {
                $this->session->setFlashdata('error', 'Exercise not found.');
                return redirect()->to(base_url('application_review'));
            }

            // Get all acknowledged applications for this exercise
            $applications = $this->applicationModel->getAcknowledgedApplicationsByExerciseId($id);

            $position = [
                'id' => 0,
                'designation' => 'All Positions in ' . $exercise['exercise_name'],
                'classification' => $exercise['exercise_year'] ?? 'N/A',
                'department' => $exercise['department'] ?? 'N/A',
                'description' => $exercise['description'] ?? ''
            ];

            $positionGroup = [
                'id' => 0,
                'group_name' => 'All Groups',
                'exercise_id' => $exercise['id']
            ];

            $data = [
                'title' => 'Applications for Exercise: ' . esc($exercise['exercise_name']),
                'menu' => 'applications',
                'exercise' => $exercise,
                'position' => $position,
                'positionGroup' => $positionGroup,
                'applications' => $applications
            ];

            return view('application_pre_screening/application_pre_screening_position_applications', $data);
        } else {
            // Base query to get exercises with acknowledged applications
            $builder = $this->exerciseModel
                ->select('exercises.*, COUNT(DISTINCT appx_application_information.id) as application_count')
                ->join('positions_groups', 'positions_groups.exercise_id = exercises.id', 'left')
                ->join('positions', 'positions.position_group_id = positions_groups.id', 'left')
                ->join('appx_application_information', 'appx_application_information.position_id = positions.id', 'left')
                ->where('exercises.org_id', $orgId)
                ->where('appx_application_information.recieved_acknowledged IS NOT NULL')
                ->groupBy('exercises.id')
                ->orderBy('exercises.exercise_name', 'ASC');

            $exercises = $builder->findAll();

            $data = [
                'title' => 'Exercises with Acknowledged Applications',
                'menu' => 'applications',
                'exercises' => $exercises
            ];

            return view('application_pre_screening/application_pre_screening_exercise_list', $data);
        }
    }

    /**
     * [GET] Display detailed view of an application for pre-screening
     * URI: /application_review/view_application/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function viewApplication($id)
    {
        // Redirect to the screening controller's show method
        return redirect()->to(base_url('application_screening/show/' . $id));
    }
}

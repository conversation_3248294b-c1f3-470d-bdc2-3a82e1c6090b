<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\PositionsGroupModel;
use App\Models\PositionsModel;
use App\Models\APPX_ApplicationInformationModel;

class RatingProcessController extends BaseController
{
    protected $exerciseModel;
    protected $positionGroupModel;
    protected $positionModel;
    protected $applicationModel;

    public function __construct()
    {
        $this->exerciseModel = new ExerciseModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionsModel();
        $this->applicationModel = new APPX_ApplicationInformationModel();
    }

    /**
     * Show the form for rating a specific application
     */
    public function rate($applicationId)
    {
        $application = $this->applicationModel->find($applicationId);

        if (!$application) {
            return redirect()->to('/rating')->with('error', 'Application not found');
        }

        $position = $this->positionModel->find($application['position_id']);
        $positionGroup = $this->positionGroupModel->find($position['position_group_id']);
        $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);

        $data = [
            'title' => 'Rate Application',
            'menu' => 'rating',
            'application' => $application,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise
        ];

        return view('rating/rating_form', $data);
    }

    /**
     * Save the rating for a specific application
     */
    public function saveRating()
    {
        $applicationId = $this->request->getPost('application_id');

        $application = $this->applicationModel->find($applicationId);
        if (!$application) {
            return redirect()->to('/rating')->with('error', 'Application not found');
        }

        // Prepare data for update
        $ratingData = [
            'rating_age' => $this->request->getPost('rating_age'),
            'rating_age_max' => $this->request->getPost('rating_age_max'),
            'rating_qualification' => $this->request->getPost('rating_qualification'),
            'rating_qualification_max' => $this->request->getPost('rating_qualification_max'),
            'rating_experience_private_non_relevant' => $this->request->getPost('rating_experience_private_non_relevant'),
            'rating_experience_private_non_relevant_max' => $this->request->getPost('rating_experience_private_non_relevant_max'),
            'rating_experience_private_relevant' => $this->request->getPost('rating_experience_private_relevant'),
            'rating_experience_private_relevant_max' => $this->request->getPost('rating_experience_private_relevant_max'),
            'rating_experience_public_non_relevant' => $this->request->getPost('rating_experience_public_non_relevant'),
            'rating_experience_public_non_relevant_max' => $this->request->getPost('rating_experience_public_non_relevant_max'),
            'rating_experience_public_relevant' => $this->request->getPost('rating_experience_public_relevant'),
            'rating_experience_public_relevant_max' => $this->request->getPost('rating_experience_public_relevant_max'),
            'rating_trainings' => $this->request->getPost('rating_trainings'),
            'rating_trainings_max' => $this->request->getPost('rating_trainings_max'),
            'rating_skills_competencies' => $this->request->getPost('rating_skills_competencies'),
            'rating_skills_competencies_max' => $this->request->getPost('rating_skills_competencies_max'),
            'rating_knowledge' => $this->request->getPost('rating_knowledge'),
            'rating_knowledge_max' => $this->request->getPost('rating_knowledge_max'),
            'rating_public_service' => $this->request->getPost('rating_public_service'),
            'rating_public_service_max' => $this->request->getPost('rating_public_service_max'),
            'rating_capability' => $this->request->getPost('rating_capability'),
            'rating_capability_max' => $this->request->getPost('rating_capability_max'),
            'rating_remarks' => $this->request->getPost('rating_remarks'),
            'rating_status' => 'completed',
            'profile_status' => 'completed',
            'rated' => 'yes',
            'rated_by' => session()->get('user_id'),
            'rated_at' => date('Y-m-d H:i:s')
        ];

        if ($this->applicationModel->update($applicationId, $ratingData)) {
            return redirect()->to('/rating/applications/' . $application['position_id'])
                ->with('success', 'Application rating saved successfully');
        } else {
            return redirect()->back()
                ->with('error', 'Failed to save rating')
                ->withInput();
        }
    }

    /**
     * View the rating details for a specific application
     */
    public function viewRating($applicationId)
    {
        $application = $this->applicationModel->find($applicationId);

        if (!$application) {
            return redirect()->to('/rating')->with('error', 'Application not found');
        }

        $position = $this->positionModel->find($application['position_id']);
        $positionGroup = $this->positionGroupModel->find($position['position_group_id']);
        $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);

        $ratingScore = $this->applicationModel->getApplicationRatingScore($applicationId);

        $data = [
            'title' => 'View Rating',
            'menu' => 'rating',
            'application' => $application,
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'ratingScore' => $ratingScore
        ];

        return view('rating/rating_view', $data);
    }
}

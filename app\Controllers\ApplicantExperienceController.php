<?php

namespace App\Controllers;

use App\Models\ApplicantsExperiencesModel;
use App\Models\ApplicantEducationModel;
use App\Models\EducationModel;

class ApplicantExperienceController extends BaseController
{
    protected $experiencesModel;
    protected $applicantEducationModel;
    protected $educationModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
        $this->experiencesModel = new ApplicantsExperiencesModel();
        $this->applicantEducationModel = new ApplicantEducationModel();
        $this->educationModel = new EducationModel();
    }

    public function addExperience()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'applicant_id' => $applicant_id,
            'employer' => $this->request->getPost('employer'),
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'achievements' => $this->request->getPost('achievements'),
            'work_description' => $this->request->getPost('work_description'),
            'created_by' => $applicant_id,
            'updated_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->insert($data);

            // Get the newly added experience with proper date formatting
            $newExperience = $this->experiencesModel->find($this->experiencesModel->getInsertID());

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Work experience added successfully',
                'experience' => $newExperience
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error adding work experience: ' . $e->getMessage()
            ]);
        }
    }

    public function updateExperience()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $experience_id = $this->request->getPost('id');

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Experience not found or access denied'
            ]);
        }

        $data = [
            'employer' => $this->request->getPost('employer'),
            'employer_contacts_address' => $this->request->getPost('employer_contacts_address'),
            'position' => $this->request->getPost('position'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'achievements' => $this->request->getPost('achievements'),
            'work_description' => $this->request->getPost('work_description'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->experiencesModel->update($experience_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Work experience updated successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating work experience: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteExperience()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $experience_id = $this->request->getPost('id');

        // Verify ownership
        $experience = $this->experiencesModel->where('id', $experience_id)
                                           ->where('applicant_id', $applicant_id)
                                           ->first();

        if (!$experience) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Experience not found or access denied'
            ]);
        }

        try {
            $this->experiencesModel->delete($experience_id);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Work experience deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting work experience: ' . $e->getMessage()
            ]);
        }
    }

    public function addEducation()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'applicant_id' => $applicant_id,
            'education_id' => $this->request->getPost('education_id'),
            'institution' => $this->request->getPost('institution'),
            'qualification' => $this->request->getPost('qualification'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'grade_achieved' => $this->request->getPost('grade_achieved'),
            'created_by' => $applicant_id,
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantEducationModel->insert($data);

            // Get the newly added education record
            $newEducation = $this->applicantEducationModel->find($this->applicantEducationModel->getInsertID());

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Education record added successfully',
                'education' => $newEducation
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error adding education record: ' . $e->getMessage()
            ]);
        }
    }

    public function updateEducation()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $education_id = $this->request->getPost('id');

        // Verify ownership
        $education = $this->applicantEducationModel->where('id', $education_id)
                                                 ->where('applicant_id', $applicant_id)
                                                 ->first();

        if (!$education) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Education record not found or access denied'
            ]);
        }

        $data = [
            'education_id' => $this->request->getPost('education_id'),
            'institution' => $this->request->getPost('institution'),
            'qualification' => $this->request->getPost('qualification'),
            'date_from' => $this->request->getPost('date_from'),
            'date_to' => $this->request->getPost('date_to'),
            'grade_achieved' => $this->request->getPost('grade_achieved'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantEducationModel->update($education_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Education record updated successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating education record: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteEducation()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $education_id = $this->request->getPost('id');

        // Verify ownership
        $education = $this->applicantEducationModel->where('id', $education_id)
                                                 ->where('applicant_id', $applicant_id)
                                                 ->first();

        if (!$education) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Education record not found or access denied'
            ]);
        }

        try {
            $this->applicantEducationModel->delete($education_id);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Education record deleted successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error deleting education record: ' . $e->getMessage()
            ]);
        }
    }
}

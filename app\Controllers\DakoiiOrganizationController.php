<?php

namespace App\Controllers;

use App\Models\orgModel;
use App\Models\usersModel;
use App\Models\countryModel;
use App\Models\provinceModel;

class DakoiiOrganizationController extends BaseController
{
    protected $orgModel;
    protected $usersModel;
    protected $countryModel;
    protected $provinceModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->orgModel = new orgModel();
        $this->usersModel = new usersModel();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
    }

    // Organization Methods
    public function organizationList()
    {
        $data['title'] = "Organizations";
        $data['menu'] = "organizations";
        $data['organizations'] = $this->orgModel->findAll();
        echo view('dakoii/dakoii_organizations', $data);
    }

    public function organizationCreateForm()
    {
        $data['title'] = "Create Organization";
        $data['menu'] = "organizations";
        echo view('dakoii/dakoii_organization_create', $data);
    }

    public function organizationStore()
    {
        if (!$this->validate(['org_name' => 'required'])) {
            session()->setFlashdata('error', 'Enter valid Data');
            return redirect()->to('dakoii/dashboard');
        }

        $orgcode = rand(11111, 99999);
        if (!empty($this->orgModel->where('org_code', $orgcode)->first())) {
            $orgcode = rand(11111, 99999);
        }

        $data = [
            'org_code' => $orgcode,
            'org_name' => $this->request->getVar('org_name'),
            'description' => $this->request->getVar('description'),
            'is_active' => 1,
        ];

        $this->orgModel->insert($data);

        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile->isValid() && $logoFile->getSize() > 0) {
            // Make sure the upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
            $logoFile->move($uploadPath, $newName);

            // Store only the relative path without base_url
            $data['orglogo'] = 'public/uploads/org_logo/' . $newName;

            $getid = $this->orgModel->where('orgcode', $orgcode)->first();
            $this->orgModel->update($getid['id'], $data);

            // Log the upload for debugging
            log_message('info', 'Organization logo uploaded: ' . $data['orglogo']);
        }

        session()->setFlashdata('success', 'Organization Created');
        return redirect()->to(base_url('dakoii/organization/view/' . $orgcode));
    }

    public function organizationView($orgcode)
    {
        $org = $this->orgModel->where('org_code', $orgcode)->first();
        if (empty($org)) {
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        $data['title'] = "Organization Details";
        $data['menu'] = "organizations";
        $data['org'] = $org;
        $data['admins'] = $this->usersModel->where('orgcode', $orgcode)->findAll();

        // Get the province and country
        if (!empty($org['location_lock_country'])) {
            $country = $this->countryModel->find($org['location_lock_country']);
            if (!empty($country)) {
                $data['country_name'] = $country['name'];
            }
        }

        if (!empty($org['location_lock_province'])) {
            $province = $this->provinceModel->find($org['location_lock_province']);
            if (!empty($province)) {
                $data['province_name'] = $province['name'];
            }
        }

        // Get all provinces for the set country
        $data['set_country'] = $this->countryModel->findAll()[0];
        $data['get_provinces'] = $this->provinceModel->findAll();

        // Get exercises for this organization
        $exerciseModel = new \App\Models\ExerciseModel();
        $data['exercises'] = $exerciseModel->where('org_id', $org['id'])->findAll();

        // Check if the user has permission to add exercises
        $data['can_add_exercise'] = true; // Default to true, adjust based on your permission system

        echo view('dakoii/dakoii_open_org', $data);
    }

    public function organizationEditForm($orgcode)
    {
        $org = $this->orgModel->where('org_code', $orgcode)->first();
        if (empty($org)) {
            return redirect()->to(base_url('dakoii/dashboard'));
        }

        $data['title'] = "Edit Organization";
        $data['menu'] = "organizations";
        $data['org'] = $org;

        // Get the province and country
        if (!empty($org['location_lock_country'])) {
            $country = $this->countryModel->find($org['location_lock_country']);
            if (!empty($country)) {
                $data['country_name'] = $country['name'];
            }
        }

        if (!empty($org['location_lock_province'])) {
            $province = $this->provinceModel->find($org['location_lock_province']);
            if (!empty($province)) {
                $data['province_name'] = $province['name'];
            }
        }

        // Get all provinces for the set country
        $data['set_country'] = $this->countryModel->findAll()[0];
        $data['get_provinces'] = $this->provinceModel->findAll();

        echo view('dakoii/dakoii_organization_edit', $data);
    }

    public function organizationUpdate()
    {
        if (!$this->validate(['org_name' => 'required'])) {
            session()->setFlashdata('error', 'Organization name is required');
            return redirect()->back();
        }

        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        $addprov = "";
        if (!empty($this->request->getVar('country'))) {
            $addprov = $this->request->getVar('province');
        }

        $data = [
            'org_name' => $this->request->getVar('org_name'),
            'description' => $this->request->getVar('description'),
            'location_lock_country' => $this->request->getVar('country'),
            'location_lock_province' => $addprov,
            'is_active' => $this->request->getVar('status'),
        ];

        // Process logo upload
        $logoFile = $this->request->getFile('org_logo');
        if ($logoFile && $logoFile->isValid() && $logoFile->getSize() > 0) {
            // Make sure the upload directory exists
            $uploadPath = ROOTPATH . 'public/uploads/org_logo/';
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }

            // Generate unique name and move the file
            $newName = $orgcode . "_" . time() . '.' . $logoFile->getExtension();
            $logoFile->move($uploadPath, $newName);

            // Set the relative path for storage
            $data['logo_path'] = 'public/uploads/org_logo/' . $newName;

            // Log the upload for debugging
            log_message('info', 'Organization logo uploaded: ' . $data['logo_path']);
        }

        $this->orgModel->update($id, $data);
        session()->setFlashdata('success', 'Organization Updated Successfully');
        return redirect()->back();
    }

    public function organizationUpdateLicense()
    {
        $id = $this->request->getVar('id');
        $orgcode = $this->request->getVar('orgcode');

        $data = [
            'license_status' => $this->request->getVar('license_status'),
        ];

        $this->orgModel->update($id, $data);

        session()->setFlashdata('success', 'License Status Changed');
        return redirect()->to('dakoii/organization/view/'.$orgcode);
    }
}

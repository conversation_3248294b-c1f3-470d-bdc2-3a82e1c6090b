<?php

namespace App\Controllers;

use App\Models\dakoiiUsersModel;
use App\Models\usersModel;
use App\Models\orgModel;
use App\Models\provinceModel;
use App\Models\selectionModel;
use App\Models\EducationModel;

class DakoiiDashboardController extends BaseController
{
    protected $dusersModel;
    protected $usersModel;
    protected $orgModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $selectionModel;
    protected $educationModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->dusersModel = new dakoiiUsersModel();
        $this->usersModel = new usersModel();
        $this->orgModel = new orgModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new \App\Models\districtModel();
        $this->llgModel = new \App\Models\llgModel();
        $this->wardModel = new \App\Models\wardModel();
        $this->selectionModel = new selectionModel();
        $this->educationModel = new EducationModel();
    }

    // Dashboard Methods
    public function dashboard()
    {
        $data['title'] = "Dashboard";
        $data['menu'] = "dashboard";

        $data['dusers'] = $this->dusersModel->findAll();
        $data['admins'] = $this->usersModel->findAll();
        $data['org'] = $this->orgModel->orderBy('id', 'DESC')->findAll();
        $data['selections'] = $this->selectionModel->orderBy('id', 'DESC')->findAll();

        $data['provinces_count'] = $this->provinceModel->countAllResults();
        $data['districts_count'] = $this->districtModel->countAllResults();
        $data['llgs_count'] = $this->llgModel->countAllResults();
        $data['wards_count'] = $this->wardModel->countAllResults();

        $data['province_stats'] = $this->getProvinceStats();

        $data['education'] = $this->educationModel->findAll();

        // Get pending exercises with publish_request status
        $exerciseModel = new \App\Models\ExerciseModel();
        $data['pending_exercises'] = $exerciseModel->where('status', 'publish_request')->findAll();

        // If we have pending exercises, let's get their organization names
        if (!empty($data['pending_exercises'])) {
            foreach ($data['pending_exercises'] as &$exercise) {
                if (!empty($exercise['org_id'])) {
                    $org = $this->orgModel->find($exercise['org_id']);
                    if ($org) {
                        // Use the orgModel's method to get the organization name
                        $exercise['org_name'] = $this->orgModel->getOrganizationName($org);
                    }
                }
            }
        }

        echo view('dakoii/dakoii_ddash', $data);
    }

    private function getProvinceStats()
    {
        $provinces = $this->provinceModel->findAll();
        $stats = [];

        foreach ($provinces as $province) {
            $districts = $this->districtModel->where('province_id', $province['id'])->findAll();
            $district_ids = array_column($districts, 'id');

            $llgs_count = 0;
            $wards_count = 0;

            if (!empty($district_ids)) {
                $llgs_count = $this->llgModel->whereIn('district_id', $district_ids)->countAllResults();
                $llgs = $this->llgModel->whereIn('district_id', $district_ids)->findAll();
                $llg_ids = array_column($llgs, 'id');

                if (!empty($llg_ids)) {
                    $wards_count = $this->wardModel->whereIn('llg_id', $llg_ids)->countAllResults();
                }
            }

            $stats[$province['id']] = [
                'name' => $province['name'],
                'districts' => count($districts),
                'llgs' => $llgs_count,
                'wards' => $wards_count
            ];
        }

        return $stats;
    }
}

<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\PositionsGroupModel;
use App\Models\PositionsModel;
use App\Models\APPX_ApplicationInformationModel;

class RatingManagementController extends BaseController
{
    protected $exerciseModel;
    protected $positionGroupModel;
    protected $positionModel;
    protected $applicationModel;

    public function __construct()
    {
        $this->exerciseModel = new ExerciseModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionsModel();
        $this->applicationModel = new APPX_ApplicationInformationModel();
    }

    /**
     * Display a listing of the exercises available for rating
     */
    public function index()
    {
        // Get exercises in selection status
        $exercises = $this->exerciseModel->where('status', 'selection')->findAll();

        $data = [
            'title' => 'Rating - Exercises',
            'menu' => 'rating',
            'exercises' => $exercises
        ];

       return view('rating/rating_exercise_list', $data);
    }

    /**
     * Display position groups for a specific exercise
     */
    public function positionGroups($exerciseId)
    {
        $exercise = $this->exerciseModel->find($exerciseId);

        if (!$exercise) {
            return redirect()->to('/rating')->with('error', 'Exercise not found');
        }

        // Get all position groups for this exercise
        $allPositionGroups = $this->positionGroupModel->where('exercise_id', $exerciseId)->findAll();

        // Filter position groups to only include those with applications that have passed pre-screening
        $filteredPositionGroups = [];
        foreach ($allPositionGroups as $group) {
            // Get positions in this group
            $positions = $this->positionModel->where('position_group_id', $group['id'])->findAll();

            // Check if any position has applications that have passed pre-screening
            $hasApplications = false;
            foreach ($positions as $position) {
                $applications = $this->applicationModel
                    ->where('position_id', $position['id'])
                    ->where('pre_screened_status', 'passed')
                    ->findAll();

                if (!empty($applications)) {
                    $hasApplications = true;
                    break;
                }
            }

            if ($hasApplications) {
                $filteredPositionGroups[] = $group;
            }
        }

        $data = [
            'title' => 'Rating - Position Groups',
            'menu' => 'rating',
            'exercise' => $exercise,
            'positionGroups' => $filteredPositionGroups
        ];

        return view('rating/rating_position_groups', $data);
    }

    /**
     * Display positions for a specific position group
     */
    public function positions($groupId)
    {
        $positionGroup = $this->positionGroupModel->find($groupId);

        if (!$positionGroup) {
            return redirect()->to('/rating')->with('error', 'Position group not found');
        }

        $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);

        // Get all positions in this group
        $allPositions = $this->positionModel->where('position_group_id', $groupId)->findAll();

        // Filter positions to only include those with applications that have passed pre-screening
        $filteredPositions = [];
        foreach ($allPositions as $position) {
            $applications = $this->applicationModel
                ->where('position_id', $position['id'])
                ->where('pre_screened_status', 'passed')
                ->findAll();

            if (!empty($applications)) {
                $filteredPositions[] = $position;
            }
        }

        $data = [
            'title' => 'Rating - Positions',
            'menu' => 'rating',
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'positions' => $filteredPositions
        ];

        return view('rating/rating_positions', $data);
    }

    /**
     * Display applications for a specific position
     */
    public function applications($positionId)
    {
        $position = $this->positionModel->find($positionId);

        if (!$position) {
            return redirect()->to('/rating')->with('error', 'Position not found');
        }

        $positionGroup = $this->positionGroupModel->find($position['position_group_id']);
        $exercise = $this->exerciseModel->find($positionGroup['exercise_id']);

        // Get all applications that have passed pre-screening
        $applications = $this->applicationModel
            ->where('pre_screened_status', 'passed')
            ->where('profile_status', 'completed')
            ->findAll();

        $data = [
            'title' => 'Rating - Applications',
            'menu' => 'rating',
            'position' => $position,
            'positionGroup' => $positionGroup,
            'exercise' => $exercise,
            'applications' => $applications
        ];

        return view('rating/rating_applications', $data);
    }
}

<?php

namespace App\Controllers;

use App\Models\ExerciseModel;
use App\Models\PositionsGroupModel;
use App\Models\PositionsModel;
use App\Models\APPX_ApplicationInformationModel;

class RatingAIController extends BaseController
{
    protected $exerciseModel;
    protected $positionGroupModel;
    protected $positionModel;
    protected $applicationModel;

    public function __construct()
    {
        $this->exerciseModel = new ExerciseModel();
        $this->positionGroupModel = new PositionsGroupModel();
        $this->positionModel = new PositionsModel();
        $this->applicationModel = new APPX_ApplicationInformationModel();
    }

    /**
     * Generate AI analysis for a specific application
     *
     * @return \CodeIgniter\HTTP\ResponseInterface
     */
    public function generate_ai_analysis()
    {
        // Check if it's an AJAX request
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid request method'
            ]);
        }
        
        // Get parameters
        $applicationId = $this->request->getPost('application_id');
        $positionId = $this->request->getPost('position_id');
        
        // Validate application ID
        if (!$applicationId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Application ID is required'
            ]);
        }
        
        // Load application data
        $application = $this->applicationModel->find($applicationId);
        if (!$application) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Application not found'
            ]);
        }
        
        // Load position data
        $position = $this->positionModel->find($positionId);
        if (!$position) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Position not found'
            ]);
        }
        
        // Load Gemini AI helper
        helper('GeminiAI');
        
        // Prepare profile data
        $profileData = [];
        
        // Parse JSON profile details if available
        if (!empty($application['profile_details'])) {
            try {
                $profileDetails = json_decode($application['profile_details'], true);
                if (isset($profileDetails['profile_data'])) {
                    $profileData = $profileDetails['profile_data'];
                }
            } catch (\Exception $e) {
                log_message('error', 'Error parsing profile details: ' . $e->getMessage());
            }
        }
        
        // If no structured profile data, create basic data
        if (empty($profileData)) {
            $profileData = [
                'core_identifiers' => [
                    'name' => $application['fname'] . ' ' . $application['lname'],
                    'date_of_birth' => !empty($application['dobirth']) ? date('Y-m-d', strtotime($application['dobirth'])) : null,
                    'sex' => $application['gender'] ?? null,
                    'place_of_origin' => $application['place_of_origin'] ?? null,
                    'citizenship' => $application['citizenship'] ?? null
                ],
                'employment_information' => [
                    'current_position' => $application['current_position'] ?? null,
                    'current_employer' => $application['current_employer'] ?? null,
                    'current_salary' => $application['current_salary'] ?? null
                ],
                'application_details' => [
                    'position_applied_for' => $position['designation'] ?? 'Unknown Position'
                ]
            ];
        }
        
        // Get rating form categories and descriptions
        $ratingCategories = $this->getRatingCategories();
        
        // Add position information
        $analysisData = [
            'applicant' => $profileData,
            'position' => [
                'title' => $position['designation'] ?? null,
                'requirements' => $position['requirements'] ?? null,
                'qualifications' => $position['qualifications'] ?? null,
                'knowledge' => $position['knowledge'] ?? null,
                'skills_competencies' => $position['skills_competencies'] ?? null,
                'job_experiences' => $position['job_experiences'] ?? null
            ],
            'rating_categories' => $ratingCategories
        ];
        
        // Generate prompt for AI analysis with rating suggestions
        $prompt = $this->generateAnalysisPrompt();

        try {
            // Call Gemini AI API
            $response = gemini_analyze_applicant($analysisData, $prompt);
            
            if (isset($response['success']) && $response['success']) {
                return $this->response->setJSON([
                    'success' => true,
                    'analysis' => $response['analysis']
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => $response['message'] ?? 'Failed to generate AI analysis'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error generating AI analysis: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get rating categories configuration
     */
    private function getRatingCategories()
    {
        return [
            'rating_age' => [
                'name' => 'Age',
                'options' => [
                    '0' => '65+ years',
                    '1' => '60-64 years',
                    '2' => '54-59 years',
                    '3' => '48-53 years',
                    '4' => '42-47 years',
                    '5' => '36-41 years',
                    '6' => '30-35 years',
                    '7' => '24-29 years',
                    '8' => '18-23 years'
                ],
                'max' => 8
            ],
            'rating_qualification' => [
                'name' => 'Education Qualification',
                'options' => [
                    '1' => 'Certificate 1',
                    '2' => 'Certificate 2',
                    '3' => 'Certificate 3',
                    '4' => 'Certificate 4/Higher Education Certificate',
                    '5' => 'Diploma',
                    '6' => 'Advanced Diploma/Associate Degree',
                    '7' => 'Bachelor\'s Degree',
                    '8' => 'Honors/Post Graduate Certificate/Diploma',
                    '9' => 'Master\'s Degree',
                    '10' => 'Doctorate'
                ],
                'max' => 10
            ],
            'rating_capability' => [
                'name' => 'Capability Rating',
                'options' => [
                    '1' => 'No Capability',
                    '2' => 'Low Capability',
                    '3' => 'Average/Competent',
                    '4' => 'Above Average',
                    '5' => 'Very Capable'
                ],
                'max' => 5
            ],
            'rating_public_service' => [
                'name' => 'Public Service Status',
                'options' => [
                    '1' => 'Non-Public Servant',
                    '2' => 'Public Servant',
                    '3' => 'Section 39'
                ],
                'max' => 3
            ],
            'rating_skills_competencies' => [
                'name' => 'Skills and Competencies',
                'options' => [
                    '0' => 'No Skills',
                    '1' => 'Limited Skills',
                    '2' => 'Below Average Skills',
                    '3' => 'Average Skills',
                    '4' => 'Above Average Skills',
                    '5' => 'Exceptional Skills'
                ],
                'max' => 5
            ],
            'rating_experience_private_non_relevant' => [
                'name' => 'Private Sector Work Experience (Non-Relevant)',
                'options' => [
                    '0' => 'No/Less Non-Relevant Exp. 0-4 years',
                    '1' => 'Private Non-Relevant 5-9 years',
                    '2' => 'Private Non-Relevant 10-14 years',
                    '3' => 'Private Non-Relevant 15-19 years',
                    '4' => 'Private Non-Relevant 20+ years'
                ],
                'max' => 4
            ],
            'rating_experience_private_relevant' => [
                'name' => 'Private Sector Work Experience (Relevant)',
                'options' => [
                    '1' => 'Private Relevant 0-4 years',
                    '2' => 'Private Relevant 5-9 years',
                    '3' => 'Private Relevant 10-14 years',
                    '4' => 'Private Relevant 15-19 years',
                    '5' => 'Private Relevant 20+ years'
                ],
                'max' => 5
            ],
            'rating_experience_public_non_relevant' => [
                'name' => 'Public Sector Work Experience (Non-Relevant)',
                'options' => [
                    '0' => 'No Public Non-Relevant Exp.',
                    '1' => 'Public Non-Relevant 4-8 years',
                    '2' => 'Public Non-Relevant 5-9 years',
                    '3' => 'Public Non-Relevant 10-14 years',
                    '4' => 'Public Non-Relevant 15-19 years',
                    '5' => 'Public Non-Relevant 20+ years'
                ],
                'max' => 5
            ],
            'rating_experience_public_relevant' => [
                'name' => 'Public Sector Work Experience (Relevant)',
                'options' => [
                    '1' => 'Public Relevant 0-4 years',
                    '2' => 'Public Relevant 5-9 years',
                    '3' => 'Public Relevant 10-14 years',
                    '4' => 'Public Relevant 15-19 years',
                    '5' => 'Public Relevant 20+ years'
                ],
                'max' => 5
            ],
            'rating_supervisory_level' => [
                'name' => 'Supervisory Level',
                'options' => [
                    '0' => 'No Supervisory Experience',
                    '1' => 'Individual Contributor',
                    '2' => 'Team Leader/Supervisor',
                    '3' => 'Middle Management',
                    '4' => 'Senior Management',
                    '5' => 'Top Level (CEO/MD/ED)'
                ],
                'max' => 5
            ],
            'rating_overall_assessment' => [
                'name' => 'Overall Assessment',
                'options' => [
                    '1' => 'Poor',
                    '2' => 'Below Average',
                    '3' => 'Average',
                    '4' => 'Above Average',
                    '5' => 'Exceptional'
                ],
                'max' => 5
            ]
        ];
    }

    /**
     * Generate the analysis prompt for AI
     */
    private function generateAnalysisPrompt()
    {
        return "You are a professional recruitment analyst. Compare the position specifications with the applicant's qualifications and provide a detailed analysis.

For each section, analyze how well the applicant meets the position requirements, and suggest an appropriate rating based on the rating categories provided:

1. **Qualification Match**: Compare the applicant's educational qualifications with position requirements. Suggest an Education Qualification rating (1-10).

2. **Experience Relevance**: 
   - Analyze both public and private sector experience
   - Determine how relevant the experience is to the position
   - Suggest ratings for:
     - Private Sector Work Experience (Non-Relevant) (0-4)
     - Private Sector Work Experience (Relevant) (1-5)
     - Public Sector Work Experience (Non-Relevant) (0-5)
     - Public Sector Work Experience (Relevant) (1-5)
     - Supervisory Level (0-5)

3. **Skills Assessment**: Compare applicant's skills and competencies with position requirements. Suggest ratings for:
   - Skills and Competencies (0-5)
   - Capability Rating (1-5)

4. **Age Consideration**: Based on the applicant's age, suggest an appropriate Age rating (0-8).

5. **Public Service Status**: Based on the applicant's employment history, suggest a Public Service Status rating (1-3).

6. **Strengths**: Identify 3-5 key strengths of the applicant for this position.

7. **Areas of Concern**: Note any gaps or mismatches in the applicant's profile.

8. **Overall Assessment**: Provide an overall assessment of fit for the position and suggest an Overall Assessment rating (1-5).

For EACH rating category, explicitly state your suggested score with justification, using this format:
'Suggested Rating: [NUMBER] - [DESCRIPTION]' (e.g., 'Suggested Rating: 4 - Above Average Skills')

Be objective, thorough, and specific. Format with headings and bullet points for readability.

Here's the applicant and position data:
";
    }
}

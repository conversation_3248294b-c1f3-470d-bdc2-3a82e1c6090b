<?php

namespace App\Controllers;

use App\Models\EducationModel;

class DakoiiEducationController extends BaseController
{
    protected $educationModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->educationModel = new EducationModel();
    }

    // Education Management Methods
    public function educationCreate()
    {
        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $this->request->getPost('icon'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('user_id')
        ];

        if ($this->educationModel->insert($data)) {
            session()->setFlashdata('success', 'Education item added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add education item');
        }
        return redirect()->to('dakoii/dashboard');
    }

    public function educationUpdate()
    {
        $id = $this->request->getPost('id');
        $data = [
            'name' => $this->request->getPost('name'),
            'icon' => $this->request->getPost('icon'),
            'color_code' => $this->request->getPost('color_code'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('user_id')
        ];

        if ($this->educationModel->update($id, $data)) {
            session()->setFlashdata('success', 'Education item updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update education item');
        }
        return redirect()->to('dakoii/dashboard');
    }

    public function educationDelete($id)
    {
        if ($this->educationModel->delete($id)) {
            session()->setFlashdata('success', 'Education item deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete education item');
        }
        return redirect()->to('dakoii/dashboard');
    }

    public function educationList()
    {
        $data['title'] = "Education Management";
        $data['menu'] = "education";
        $data['education'] = $this->educationModel->findAll();
        echo view('dakoii/dakoii_education', $data);
    }
}

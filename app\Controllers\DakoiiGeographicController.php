<?php

namespace App\Controllers;

use App\Models\countryModel;
use App\Models\provinceModel;

class DakoiiGeographicController extends BaseController
{
    protected $countryModel;
    protected $provinceModel;
    protected $districtModel;
    protected $llgModel;
    protected $wardModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
        $this->countryModel = new countryModel();
        $this->provinceModel = new provinceModel();
        $this->districtModel = new \App\Models\districtModel();
        $this->llgModel = new \App\Models\llgModel();
        $this->wardModel = new \App\Models\wardModel();
    }

    // Province Management Methods
    public function provinceList()
    {
        $data['title'] = "Provinces";
        $data['menu'] = "provinces";

        $data['set_country'] = $this->countryModel->where('code', COUNTRY_CODE)->first();
        $data['provinces'] = $this->provinceModel
            ->where('country_id', $data['set_country']['id'])
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_provinces', $data);
    }

    public function provinceCreate()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'provincecode' => 'required|is_unique[adx_province.provincecode]'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'provincecode' => $this->request->getPost('provincecode'),
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->provinceModel->insert($data)) {
                session()->setFlashdata('success', 'Province "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add province. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function provinceUpdate()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'provincecode' => 'required|is_unique[adx_province.provincecode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'provincecode' => $this->request->getPost('provincecode'),
                'country_id' => $this->request->getPost('country_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->provinceModel->update($id, $data)) {
                session()->setFlashdata('success', 'Province updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update province');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function provinceDelete($id)
    {
        if ($this->provinceModel->delete($id)) {
            session()->setFlashdata('success', 'Province deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete province');
        }
        return redirect()->back();
    }

    public function provinceGet($id)
    {
        $province = $this->provinceModel->find($id);
        return $this->response->setJSON($province);
    }

    // District Management Methods
    public function districtList($provinceId)
    {
        $data['title'] = "Districts";
        $data['menu'] = "provinces";

        $data['province'] = $this->provinceModel->find($provinceId);
        if (empty($data['province'])) {
            return redirect()->to('dakoii/provinces');
        }

        $data['districts'] = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        echo view('dakoii/dakoii_districts', $data);
    }

    public function districtGetByProvince($provinceId)
    {
        $districts = $this->districtModel
            ->where('province_id', $provinceId)
            ->orderBy('name', 'asc')
            ->findAll();

        return $this->response->setJSON($districts);
    }

    public function districtCreate()
    {
        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'districtcode' => 'required|is_unique[adx_district.districtcode]'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'districtcode' => $this->request->getPost('districtcode'),
                'province_id' => $this->request->getPost('province_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->districtModel->insert($data)) {
                session()->setFlashdata('success', 'District "' . $data['name'] . '" has been added successfully');
            } else {
                session()->setFlashdata('error', 'Failed to add district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function districtUpdate()
    {
        $id = $this->request->getPost('id');

        $rules = [
            'name' => 'required|min_length[3]|max_length[100]',
            'districtcode' => 'required|is_unique[adx_district.districtcode,id,' . $id . ']'
        ];

        if ($this->validate($rules)) {
            $data = [
                'name' => $this->request->getPost('name'),
                'districtcode' => $this->request->getPost('districtcode'),
                'province_id' => $this->request->getPost('province_id'),
                'json_id' => $this->request->getPost('json_id')
            ];

            if ($this->districtModel->update($id, $data)) {
                session()->setFlashdata('success', 'District updated successfully');
            } else {
                session()->setFlashdata('error', 'Failed to update district');
            }
        } else {
            session()->setFlashdata('error', 'Validation failed: ' . implode(', ', $this->validator->getErrors()));
        }
        return redirect()->back();
    }

    public function districtDelete($id)
    {
        $district = $this->districtModel->find($id);
        if ($district) {
            if ($this->districtModel->delete($id)) {
                session()->setFlashdata('success', 'District "' . $district['name'] . '" has been deleted successfully');
            } else {
                session()->setFlashdata('error', 'Failed to delete district. Please try again.');
            }
        } else {
            session()->setFlashdata('error', 'District not found');
        }
        return redirect()->back();
    }
}

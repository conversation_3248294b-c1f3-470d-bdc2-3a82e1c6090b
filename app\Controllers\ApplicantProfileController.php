<?php

namespace App\Controllers;

use App\Models\applicantsModel;
use App\Models\ApplicantsExperiencesModel;
use App\Models\ApplicantEducationModel;
use App\Models\EducationModel;
use App\Models\ApplicantFilesModel;

class ApplicantProfileController extends BaseController
{
    protected $applicantsModel;
    protected $experiencesModel;
    protected $applicantEducationModel;
    protected $educationModel;
    protected $applicantFilesModel;
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
        $this->applicantsModel = new applicantsModel();
        $this->experiencesModel = new ApplicantsExperiencesModel();
        $this->applicantEducationModel = new ApplicantEducationModel();
        $this->educationModel = new EducationModel();
        $this->applicantFilesModel = new ApplicantFilesModel();
    }

    public function profile()
    {
        $applicant_id = session()->get('applicant_id');
        $applicant = $this->applicantsModel->find($applicant_id);

        if (!$applicant) {
            return redirect()->to('/')->with('error', 'Applicant not found');
        }

        // Get work experiences
        $experiences = $this->experiencesModel->where('applicant_id', $applicant_id)
                                            ->orderBy('date_from', 'DESC')
                                            ->findAll();

        // Get applicant's education records using automatic model features
        $education = $this->applicantEducationModel->where('applicant_id', $applicant_id)
                                                 ->orderBy('date_from', 'DESC')
                                                 ->findAll();

        // Get education levels from adx_education table for dropdowns
        $education_data = $this->educationModel->findAll();

        // Get applicant's files
        $files = $this->applicantFilesModel->where('applicant_id', $applicant_id)
                                          ->orderBy('created_at', 'DESC')
                                          ->findAll();

        $data = [
            'title' => 'Edit Profile',
            'menu' => 'profile',
            'applicant' => $applicant,
            'experiences' => $experiences,
            'education' => $education,
            'education_data' => $education_data,
            'files' => $files,
            'education_levels' => [
                1 => 'Elementary',
                2 => 'High School',
                3 => 'Vocational',
                4 => 'College',
                5 => 'Post Graduate'
            ]
        ];

        return view('applicant/applicant_profile', $data);
    }

    public function uploadPhoto()
    {
        $applicant_id = session()->get('applicant_id');
        $photo = $this->request->getFile('id_photo');

        // Debug response for troubleshooting
        if (!$photo || !$photo->isValid() || $photo->hasMoved()) {
            $debug = [
                'photo_exists' => !empty($photo),
                'is_valid' => $photo ? $photo->isValid() : false,
                'has_moved' => $photo ? $photo->hasMoved() : false,
                'error' => $photo ? $photo->getError() : 'No file uploaded'
            ];

            $response = [
                'success' => false,
                'message' => 'Invalid file upload',
                'debug' => $debug
            ];
            return $this->response->setJSON($response);
        }

        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
        if (!in_array($photo->getMimeType(), $allowedTypes)) {
            $response = [
                'success' => false,
                'message' => 'Only JPG, JPEG, and PNG files are allowed',
                'debug' => ['mime_type' => $photo->getMimeType()]
            ];
            return $this->response->setJSON($response);
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/photos';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        try {
            // Debug info
            $debug = [
                'upload_path_exists' => is_dir($uploadPath),
                'upload_path_writable' => is_writable($uploadPath),
                'upload_path' => $uploadPath,
                'file_name' => $photo->getName(),
                'file_size' => $photo->getSize(),
                'mime_type' => $photo->getMimeType()
            ];

            // Generate unique filename
            $newName = $applicant_id . '_' . time() . '_' . $photo->getRandomName();
            $debug['new_name'] = $newName;

            // Move file to uploads directory
            if ($photo->move($uploadPath, $newName)) {
                // Verify file was created
                $debug['file_created'] = file_exists($uploadPath . '/' . $newName);

                // Delete old photo if exists
                $applicant = $this->applicantsModel->find($applicant_id);
                if (!empty($applicant['id_photo_path'])) {
                    $oldPhotoPath = $applicant['id_photo_path'];
                    $debug['old_photo_path'] = $oldPhotoPath;
                    $debug['old_photo_exists'] = file_exists($oldPhotoPath);

                    if (file_exists(ROOTPATH . $oldPhotoPath)) {
                        unlink(ROOTPATH . $oldPhotoPath);
                        $debug['old_photo_deleted'] = true;
                    }
                }

                // Update database with new photo path - store path WITH 'public/' prefix for easy view retrieval
                $this->applicantsModel->update($applicant_id, [
                    'id_photo_path' => 'public/uploads/photos/' . $newName,
                    'updated_by' => $applicant_id
                ]);

                $debug['db_updated'] = true;
                $debug['path_stored'] = 'public/uploads/photos/' . $newName;

                $response = [
                    'success' => true,
                    'message' => 'Photo updated successfully',
                    'path' => base_url('public/uploads/photos/' . $newName),
                    'debug' => $debug
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => 'Error uploading photo',
                    'debug' => array_merge($debug, [
                        'move_error' => $photo->getError(),
                        'error_code' => $photo->getError()
                    ])
                ];
            }
        } catch (\Exception $e) {
            $response = [
                'success' => false,
                'message' => 'Error uploading photo: ' . $e->getMessage(),
                'debug' => $debug
            ];
        }

        // Return JSON response
        return $this->response->setJSON($response);
    }

    public function updatePersonal()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'fname' => $this->request->getPost('fname'),
            'lname' => $this->request->getPost('lname'),
            'gender' => $this->request->getPost('gender'),
            'dobirth' => $this->request->getPost('dobirth'),
            'contact_details' => $this->request->getPost('contact_details'),
            'location_address' => $this->request->getPost('location_address'),
            'place_of_origin' => $this->request->getPost('place_of_origin'),
            'citizenship' => $this->request->getPost('citizenship'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Personal information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateDocuments()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'id_numbers' => $this->request->getPost('id_numbers'),
            'offence_convicted' => $this->request->getPost('offence_convicted'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Documents updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating documents: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateEmployment()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'current_employer' => $this->request->getPost('current_employer'),
            'current_position' => $this->request->getPost('current_position'),
            'current_salary' => $this->request->getPost('current_salary'),
            'how_did_you_hear_about_us' => $this->request->getPost('how_did_you_hear_about_us'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Employment information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating employment information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateFamily()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'marital_status' => $this->request->getPost('marital_status'),
            'date_of_marriage' => $this->request->getPost('date_of_marriage'),
            'spouse_employer' => $this->request->getPost('spouse_employer'),
            'children' => json_encode($this->request->getPost('children') ?? []),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Family information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating family information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function updateAdditional()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        $data = [
            'publications' => $this->request->getPost('publications'),
            'awards' => $this->request->getPost('awards'),
            'referees' => $this->request->getPost('referees'),
            'updated_by' => $applicant_id
        ];

        try {
            $this->applicantsModel->update($applicant_id, $data);
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Additional information updated successfully',
                'csrf_hash' => csrf_hash()
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error updating additional information: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    public function changePassword()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $current_password = $this->request->getPost('current_password');
        $new_password = $this->request->getPost('new_password');
        $confirm_password = $this->request->getPost('confirm_password');

        // Get current applicant data
        $applicant = $this->applicantsModel->find($applicant_id);

        // Verify current password
        if (!password_verify($current_password, $applicant['password'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Current password is incorrect'
            ]);
        }

        // Verify new passwords match
        if ($new_password !== $confirm_password) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'New passwords do not match'
            ]);
        }

        try {
            $this->applicantsModel->update($applicant_id, [
                'password' => $new_password,
                'updated_by' => $applicant_id
            ]);

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Password changed successfully'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error changing password: ' . $e->getMessage()
            ]);
        }
    }
}
